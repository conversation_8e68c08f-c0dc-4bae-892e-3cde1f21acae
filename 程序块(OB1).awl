ORGANIZATION_BLOCK 程序块:OB1
TITLE=程序注释
BEGIN
Network 1 
// 对时
LD     SM0.0
AB=    VB915, 30
CALL   SBR17, &VB900
Network 2 
// 获取本地时间
LD     SM0.0
LPS
CALL   SBR18, &VB910
AB=    VB913, 0
AB=    VB914, 0
AB=    VB915, 0
=      M9.0
LRD
AB=    VB917, 2
A      M9.0
=      M9.1
LRD
AB=    VB912, 1
A      M9.0
=      M9.2
LRD
LDB=   VB911, 1
OB=    VB911, 4
OB=    VB911, 7
OB=    VB911, 10
ALD
A      M9.2
=      M9.3
LPP
AB=    VB911, 1
A      M9.2
=      M9.4
Network 3 
LD     SM0.0
CALL   SBR0
CALL   SBR4
CALL   SBR13
CALL   SBR12
CALL   SBR15
CALL   SBR14
Network 4 
// 冷化水限值初始化
LD     SM0.0
LPS
AW<    VW1000, 0
MOVW   20, VW1000
LRD
AW<    VW1002, 0
MOVW   60, VW1002
LRD
AW<    VW1004, 0
MOVW   20, VW1004
LRD
AW<    VW1006, 0
MOVW   60, VW1006
LRD
AW<    VW1008, 0
MOVW   200, VW1008
LRD
AW<    VW1010, 0
MOVW   1300, VW1010
LRD
AW<    VW1012, 0
MOVW   50, VW1012
LPP
AW<    VW1014, 0
MOVW   300, VW1014
Network 5 
// 锅炉补水限值设定
LD     SM0.0
LPS
AW<    VW1016, 0
MOVW   200, VW1016
LRD
AW<    VW1018, 0
MOVW   1900, VW1018
LRD
AW<    VW1020, 0
MOVW   50, VW1020
LPP
AW<    VW1022, 0
MOVW   400, VW1022
Network 6 
// 锅炉限值设定值
LD     SM0.0
LPS
AW<    VW1024, 0
MOVW   10, VW1024
LRD
AW<    VW1026, 0
MOVW   50, VW1026
LRD
AW<    VW1028, 0
MOVW   10, VW1028
LRD
AW<    VW1030, 0
MOVW   50, VW1030
LRD
AW<    VW1032, 0
MOVW   10, VW1032
LRD
AW<    VW1034, 0
MOVW   50, VW1034
LRD
AW<    VW1036, 0
MOVW   10, VW1036
LPP
AW<    VW1038, 0
MOVW   50, VW1038
Network 7 
// 设备启停模拟量限值初始化
LD     SM0.0
LPS
AW<    VW1050, 1
MOVW   650, VW1050
LRD
AW<    VW1052, 1
MOVW   900, VW1052
LRD
AW<    VW1054, 1
MOVW   300, VW1054
LPP
AW<    VW1056, 1
MOVW   250, VW1056
Network 8 
// 环境温湿度限值初始化
LD     SM0.0
LPS
AW<    VW1040, 0
MOVW   50, VW1040
LRD
AW<    VW1042, 0
MOVW   900, VW1042
LRD
AW<    VW1044, 0
MOVW   100, VW1044
LPP
AW<    VW1046, 0
MOVW   900, VW1046
Network 9 
// 锅炉报警写入
LD     SM0.0
LD     M5.0
O      M0.2
ALD
=      V51.0
Network 10 
LD     SM0.0
=      L60.0
LD     SM0.0
=      L63.7
LD     L60.0
CALL   SBR10, L63.7, 502, 256, 56, 100, &VB600, V7460.1, VB7445
Network 11 
// 锅炉数据转换
LD     SM0.0
MOVR   VD52, VD600
MOVR   VD56, VD604
MOVR   VD80, VD608
Network 12 
// 传感器
LD     SM0.0
LPS
MOVW   VW0, VW612
AENO
MOVW   VW2, VW614
AENO
MOVW   VW4, VW616
AENO
MOVW   VW6, VW618
LRD
MOVW   VW8, VW620
AENO
MOVW   VW10, VW622
AENO
MOVW   VW12, VW624
AENO
MOVW   VW14, VW626
LPP
MOVW   VW16, VW628
AENO
MOVW   VW18, VW630
AENO
MOVW   VW20, VW632
AENO
MOVW   VW22, VW634
Network 13 
// 流量计
LD     SM0.0
LPS
MOVD   VD300, VD636
AENO
MOVR   VD304, VD640
AENO
MOVR   VD308, VD644
AENO
MOVR   VD312, VD648
LRD
MOVD   VD340, VD652
AENO
MOVR   VD344, VD656
AENO
MOVR   VD348, VD660
AENO
MOVR   VD352, VD664
LPP
MOVD   VD380, VD668
AENO
MOVR   VD384, VD672
AENO
MOVR   VD388, VD676
AENO
MOVR   VD392, VD680
Network 14 
// 报警
LD     SM0.0
MOVW   VW50, VW684
END_ORGANIZATION_BLOCK
SUBROUTINE_BLOCK 模拟量:SBR0
TITLE=模拟量转换
BEGIN
Network 1 
// 冷却水
LD     SM0.0
LPS
CALL   SBR1, AIW16, 27648, 5530, 100, 0, VW0
A      SM0.0
LPS
AW<    VW0, VW1000
=      M10.0
LPP
AW>    VW0, VW1002
=      M10.1
LRD
CALL   SBR1, AIW18, 27648, 5530, 100, 0, VW2
LRD
A      SM0.0
LPS
AW<    VW2, VW1004
=      M10.2
LPP
AW>    VW2, VW1006
=      M10.3
LRD
LPS
CALL   SBR1, AIW20, 27648, 5530, 1400, 0, LW0
AW>    LW0, 0
MOVW   LW0, VW4
+I     VW1090, VW4
LPP
AW=    LW0, 0
MOVW   VW1090, VW4
LRD
A      SM0.0
LPS
AW<    VW4, VW1008
=      M10.4
LPP
AW>    VW4, VW1010
=      M10.5
LRD
CALL   SBR1, AIW22, 27648, 5530, 2000, 0, VW6
LPP
A      SM0.0
LPS
AW<    VW6, VW1012
=      M10.6
LPP
AW>    VW6, VW1014
=      M10.7
Network 2 
// 锅炉补水
LD     SM0.0
LPS
CALL   SBR1, AIW24, 27648, 5530, 2000, 0, LW0
AW>    LW0, VW1096
MOVW   LW0, VW8
+I     VW1096, VW8
LRD
AW<=   LW0, VW1096
MOVW   LW0, VW8
+I     VW1096, VW8
LRD
A      SM0.0
LPS
AW<    VW8, VW1016
=      M11.0
LPP
AW>    VW8, VW1018
=      M11.1
LRD
CALL   SBR1, AIW26, 27648, 5530, 2000, 0, VW10
LPP
A      SM0.0
LPS
AW<    VW10, VW1020
=      M11.2
LPP
AW>    VW10, VW1022
=      M11.3
Network 3 
// 锅炉
LD     SM0.0
LPS
CALL   SBR1, AIW28, 27648, 5530, 160, 0, LW0
AW>    LW0, 0
MOVW   LW0, VW12
+I     VW1092, VW12
LRD
AW=    LW0, 0
MOVW   VW1092, VW12
LRD
A      SM0.0
LPS
AW<    VW12, VW1024
=      M11.4
LPP
AW>    VW12, VW1026
=      M11.5
LRD
LPS
CALL   SBR1, AIW30, 27648, 5530, 160, 0, LW0
AW>    LW0, 0
MOVW   LW0, VW14
+I     VW1094, VW14
LPP
AW=    LW0, 0
MOVW   VW1094, VW14
LRD
A      SM0.0
LPS
AW<    VW14, VW1028
=      M11.6
LPP
AW>    VW14, VW1030
=      M11.7
LRD
CALL   SBR1, AIW32, 27648, 5530, 100, 0, VW16
LRD
A      SM0.0
LPS
AW<    VW16, VW1032
=      M12.0
LPP
AW>    VW16, VW1034
=      M12.1
LRD
CALL   SBR1, AIW34, 27648, 5530, 100, 0, VW18
LPP
A      SM0.0
LPS
AW<    VW18, VW1036
=      M12.2
LPP
AW>    VW18, VW1038
=      M12.3
END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK SC:SBR1
TITLE=子程序注释
VAR_INPUT
In:INT;
InUp:INT;
InDown:INT;
OutUp:INT;
OutDown:INT;
END_VAR
VAR_OUTPUT
Out:INT;
END_VAR
VAR
OutUp_R:REAL;
OutDown_R:REAL;
Out_R:REAL;
Temp:DINT;
Temp_Out:INT;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK S_RTI:SBR2
TITLE=                                              
//                                                                            LIBARY: SCALE 
//                                                                            =============
// 
// 
// LIABILITY
// Siemens AG does not accept liability of any kind for damages arising from the use of this application, except where it is obliged to by law, in cases such as damage to items used for personal purposes, personal injury, willful damage or gross negligence.
// 
// WARRANTY
// The program examples given are specific solutions to complex tasks which were worked on by Customer Support. We must also point out that it is not possible in the current state of the technology to exclude all errors in software programs under all conditions of use. The program examples were prepared according to the best of our knowledge. However, we cannot accept any liability beyond the standard guarantee for Class C software in accordance with our General Terms of Sale for Software Products for Automation and Drive Technology". The program examples can be purchased on the Internet as single licenses. They may not be transferred to a third party.
// 
// PASSWORD
// The password of the library is "1234"
// 
// S_RTI
// Scale Real to Integer
// 
//  The first step in this scaling is accomplished by reading in 
//  the values passed from the main program:  the input itself, 
//  the input scale high limit (ISH) the input scale lower limit 
//  (ISL) the output scale high limit (OSH) and the output scale 
//  low limit (OSL).
//  
//  The next step is to determine the output scale range by 
//  subtracting OSL from OSH. The output scale range is then 
//  converted to a double and then to a real.
// 
//  The input is de-scaled by subtracting ISL from it.
//  The input is then multiplied by the converted output range. 
//  
//  In the next step, ISL is subtracted from ISH to determine the 
//  input  scale range. At this point, the output range is divided 
//  by the de-scaled input to determine the output value.
//  From here, the output value is rounded and converted into a 
//  double by the ROUND operator.
// 
//  The value in the output is then checked against the boundary 
//  constraints for that type of data, to insure proper reporting 
//  of the final value.
// 
//  After the boundary check, the value is then converted from a 
//  double to an integer.  The output scale lower limit is added 
//  to the value which is then moved to the proper location for output.
// 
//  The formula is as follows:
//  Ov = [(Osh - Osl) * (Iv - Isl) / (Ish - Isl)] + Osl
// 
//  Ov	=	scaled output value
//  Iv	=	analog input value
//  Osh	=	high limit of the scale for the scaled output value
//  Osl	=	low limit of the scale for the scaled output value
//  Ish	=	high limit of the scale for the analog input value
//  Isl	=	low limit of the scale for the analog input value
// 
VAR_INPUT
Input:REAL;
ISH:REAL;
ISL:REAL;
OSH:INT;
OSL:INT;
END_VAR
VAR_OUTPUT
Output:INT;
END_VAR
VAR
Temp_D:DINT;
Temp_R:REAL;
Temp_I:INT;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK S_ITR:SBR3
TITLE=                                              
//                                                                            LIBARY: SCALE 
//                                                                            =============
// 
// 
// LIABILITY
// Siemens AG does not accept liability of any kind for damages arising from the use of this application, except where it is obliged to by law, in cases such as damage to items used for personal purposes, personal injury, willful damage or gross negligence.
// 
// WARRANTY
// The program examples given are specific solutions to complex tasks which were worked on by Customer Support. We must also point out that it is not possible in the current state of the technology to exclude all errors in software programs under all conditions of use. The program examples were prepared according to the best of our knowledge. However, we cannot accept any liability beyond the standard guarantee for Class C software in accordance with our General Terms of Sale for Software Products for Automation and Drive Technology". The program examples can be purchased on the Internet as single licenses. They may not be transferred to a third party.
// 
// PASSWORD
// The password of the library is "1234"
// 
// S_ITR
// Scale Integer to Real
// 
//  The first step in this scaling is accomplished by reading in the 
//  values passed from the main program:  the input itself, the input 
//  scale  high limit (ISH) the input scale lower limit (ISL), the 
//  output scale high limit (OSH) and the output scale low limit (OSL).
//  
//  The next step is to determine the output scale range by 
//  subtracting OSL from OSH. The input is manipulated by first 
//  de-scaling by subtracting ISL from it and then      
//  converting it to a double before finally converting it to a real.
//  The real form of the input is then multiplied by the output range. 
//  
//  In the next step, ISL is subtracted from ISH to determine the 
//  input  scale range.  The input scale range is then converted into 
//  a double, before, finally, being, converted, into a real. 
// 
//  At this point, the output range is divided by the de-scaled input 
//  to determine the final output value. To allow the final output value 
//  to be passed back to the main program the final output value is moved 
//  into the Output local variable which is then passed back.
// 
//  The formula is as follows:
//  Ov = [(Osh - Osl) * (Iv - Isl) / (Ish - Isl)] + Osl
// 
//  Ov	=	scaled output value
//  Iv	=	analog input value
//  Osh	=	high limit of the scale for the scaled output value
//  Osl	=	low limit of the scale for the scaled output value
//  Ish	=	high limit of the scale for the analog input value
//  Isl	=	low limit of the scale for the analog input value 
// 
VAR_INPUT
Input:INT;
ISH:INT;
ISL:INT;
OSH:REAL;
OSL:REAL;
END_VAR
VAR_OUTPUT
Output:REAL;
END_VAR
VAR
TEMP_D:DINT;
TEMP_R:REAL;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK 温湿度:SBR4
TITLE=子程序注释
BEGIN
Network 1 
// 第一扫秒周期, 复位各标志位和起始位。通讯失败重试次数改为0（VB5257）
LD     SM0.0
LPS
A      SM0.1
R      V5286.0, 8
R      V5287.0, 8
LPP
MOVB   0, VB5257
Network 2 
// Modbus 主站初始化完成后, 启动读写指令
LD     V5286.0
EU
S      V5286.1, 1
Network 3 
// 网络标题
// 
// 网络注释     初始化Modbus 主站通信,
// 
// EN          使能：必须保证每一扫描周期都被使能（使用SM0.0)
// 
// Mode      模式：常为1，使能 Modbus 协议功能；为0 时恢复为系统 PPI 协议
// 
// Baud       波特率：设为9600，要与从站波特率对应
// 
// Parity       校验：校验方式选择 0＝无校验；1=奇检验；2=偶检验
// 
// Port          通讯口：0 为 Port 0，1 为 Port 1
// 
// Timeout    超时：主站等待从站响应的时间，以毫秒为单位，典型的设置值为1000毫秒（1秒），
//                             允许设置的范围为 1－32767。
//                 注意： 这个值必须设置足够大以保证从站有时间响应。
// 
// Done      完成位：初始化完成，此位会自动置1。可以用该位启动 MBUS_MSG 读写操作
// 
// Error       初始化错误代码（只有在 Done 位为1时有效）：0＝ 无错误
//                                                                                                                1＝ 校验选择非法
//                                                                                                                2＝ 波特率选择非法
//                                                                                                                3 =   超时无效
//                                                                                                                4＝ 模式选择非法
//                                                                                                                9 = 端口号无效
//                                                                                                               10 = 信号板端口1 缺失或未组态
LD     SM0.0
=      L60.0
LD     SM0.0
=      L63.7
LD     L60.0
CALL   SBR5, L63.7, 9600, 0, 0, 1000, V5286.0, VB5290
Network 4 
// 读取从站保持寄存器的数据
// 
// EN             使能：同一时刻只能有一个读写功能（即 MBUS_MSG）使能
//                   注意：建议每一个读写功能（即 MBUS_MSG）都用上一个 MBUS_MSG 指令的 Done 完成位来激
//                               活，以保证所有读写指令循环进行（见程序）。 
// 
// First           读写请求位：每一个新的读写请求必须使用脉冲触发 
// 
// Slave         从站地址：可选择的范围 1－247
// 
// RW           读写操作：0＝读， 1＝写
//                  注意：1. 开关量输出和保持寄存器支持读和写功能
//                              2. 开关量输入和模拟量输入只支持读功能
// 
// Addr          读写从站的数据地址：选择读写的数据类型 00001至0xxxx－开关量输出
//                                                                                               10001至1xxxx－开关量输入
//                                                                                               30001至3xxxx－模拟量输入
//                                                                                               40001至4xxxx－保持寄存器
// 
// Count       通讯的数据个数（位或字的个数）
//                  注意： Modbus主站可读/写的最大数据量为120个字（是指每一个 MBUS_MSG 指令）
// 
// DataPtr     数据指针：1. 如果是读指令，读回的数据放到这个数据区中
//                                   2. 如果是写指令，要写出的数据放到这个数据区中
// 
// Done        读写功能完成位
// 
// Error         错误代码 只有在 Done 位为1时，错误代码才有效
// 错误代码： 0＝无错误
//                      1＝响应校验错误
//                      2＝未用
//                      3＝接收超时（从站无响应）
//                      4＝请求参数错误（slave address,Modbus address,count,RW）
//                      5＝Modbus/自由口未使能 
//                      6＝Modbus正在忙于其它请求
//                      7＝响应错误（响应不是请求的操作）
//                      8＝响应CRC校验和错误
//                     11 = 端口号无效
//                     12 = 信号板端口 1 缺失或未组态     
// 
//                  101＝ 从站不支持请求的功能
//                  102＝ 从站不支持数据地址
//                  103＝ 从站不支持此种数据类型
//                  104＝ 从站设备故障
//                  105＝ 从站接受了信息，但是响应被延迟
//                  106＝ 从站忙，拒绝了该信息
//                  107＝ 从站拒绝了信息
//                  108＝ 从站存储器奇偶错误
// 
// 
// 
// 常见的错误及其错误代码：
// 
// 1. 如果多个 MBUS_MSG 指令同时使能会造成6号错误
// 
// 2. 从站 delay 参数设的时间过长会造成3号错误
// 
// 3. 从站掉电或不运行，网络故障都会造成3号错误
// 
// 读变频器状态
LD     V5286.1
O      V5286.5
=      L60.0
LD     V5286.1
EU
LD     V5286.5
EU
OLD
=      L63.7
LD     L60.0
CALL   SBR7, L63.7, 1, 0, 40001, 2, &VB60, V5286.2, VB5291
Network 5 
LD     V5286.2
R      V5286.1, 1
R      V5286.5, 1
Network 6 
LD     V5286.2
=      L60.0
LD     V5286.2
EU
=      L63.7
LD     L60.0
CALL   SBR7, L63.7, 4, 0, 40001, 14, &VB100, V5286.3, VB5291
Network 7 
LD     V5286.3
R      V5286.2, 1
Network 8 
LD     V5286.3
=      L60.0
LD     V5286.3
EU
=      L63.7
LD     L60.0
CALL   SBR7, L63.7, 2, 0, 40001, 14, &VB140, V5286.4, VB5291
Network 9 
LD     V5286.4
R      V5286.3, 1
Network 10 
LD     V5286.4
=      L60.0
LD     V5286.4
EU
=      L63.7
LD     L60.0
CALL   SBR7, L63.7, 3, 0, 40001, 14, &VB180, V5286.5, VB5291
Network 11 
LD     V5286.5
R      V5286.4, 1
Network 12 
// 数据规整
LD     SM0.0
MOVW   VW60, VW20
-I     +400, VW20
MOVW   VW62, VW22
Network 13 
// 环境温度_湿度报警
LD     SM0.0
LPS
AW<    VW20, VW1040
=      M12.4
LRD
AW>    VW20, VW1042
=      M12.5
LRD
AW<    VW22, VW1044
=      M12.6
LPP
AW>    VW22, VW1046
=      M12.7
Network 14 
// 4#楼流量地址规整
LD     SM0.0
CALL   SBR6, VW116, VW118, LD0
MOVR   LD0, VD130
*R     100.0, VD130
CALL   SBR6, VW120, VW122, VD134
MOVR   VD130, LD0
+R     VD134, LD0
*R     100.0, LD0
ROUND  LD0, VD300
CALL   SBR6, VW108, VW110, VD304
CALL   SBR6, VW100, VW102, VD308
CALL   SBR6, VW104, VW106, VD312
Network 15 
// 制水间流量地址规整
LD     SM0.0
CALL   SBR6, VW156, VW158, LD0
MOVR   LD0, VD170
*R     100.0, VD170
CALL   SBR6, VW160, VW162, VD174
MOVR   VD170, LD4
+R     VD174, LD4
*R     100.0, LD4
ROUND  LD4, VD340
CALL   SBR6, VW148, VW150, VD344
CALL   SBR6, VW140, VW142, VD348
CALL   SBR6, VW144, VW146, VD352
Network 16 
// 去2#楼流量地址规整
LD     SM0.0
CALL   SBR6, VW196, VW198, LD0
MOVR   LD0, VD210
*R     100.0, VD210
CALL   SBR6, VW200, VW202, VD214
MOVR   VD210, LD8
+R     VD214, LD8
*R     100.0, LD8
ROUND  LD8, VD380
CALL   SBR6, VW188, VW190, VD384
CALL   SBR6, VW180, VW182, VD388
CALL   SBR6, VW184, VW186, VD392
Network 17 
// 日周月季年统计
LD     SM0.0
CALL   SBR9, VD300, &VB316, &VB1500
CALL   SBR9, VD340, &VB356, &VB1520
CALL   SBR9, VD380, &VB396, &VB1540
END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK MBUS_CTRL:SBR5
TITLE=This code is property of Siemens Industry, Inc. 
VAR_INPUT
Mode:BOOL;	//  1 = Modbus, 0 = PPI (stop Modbus)
Baud:DWORD;	//  1200, 2400 ... 115200
Parity:BYTE;	//  0 = none, 1 = odd, 2 = even
Port:BYTE;	// 0 or 1
Timeout:INT;	//  slave response timeout in milliseconds
END_VAR
VAR_OUTPUT
Done:BOOL;	//  Done flag (always set)
Error:BYTE;	//  Error status
END_VAR
VAR
AC0save:DWORD;
AC1save:DWORD;
AC2save:DWORD;
AC3save:DWORD;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK 高低字转换:SBR6
TITLE=子程序注释
VAR_INPUT
IN1:WORD;
IN2:WORD;
END_VAR
VAR_OUTPUT
OUT1:REAL;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK MBUS_MSG:SBR7
TITLE=This code is property of Siemens Industry, Inc. 
VAR_INPUT
First:BOOL;	// New message (Set to a 1 for only one scan for a new request)
Slave:BYTE;	// Slave address (0 - 247)
RW:BYTE;	// Read = 0, Write = 1
Addr:DWORD;	// Modbus addr (ie 40001)
Count:INT;	// Number of elements (1- 120 words or 1 to 1920 bits)
DataPtr:DWORD;	// Pointer to data (ie &VB100)
END_VAR
VAR_OUTPUT
Done:BOOL;	// Done flag (0 = busy, 1 = done)
Error:BYTE;	// Error (0 = no error)
END_VAR
VAR
AC0save:DWORD;
AC1save:DWORD;
AC2save:DWORD;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK MBUSM1:SBR8
TITLE=This code is property of Siemens Industry, Inc. 
VAR_OUTPUT
crc:WORD;
END_VAR
VAR
count:INT;
ptr:DWORD;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK 日周月季年统计:SBR9
TITLE=子程序注释
VAR_INPUT
LJ:DWORD;	// 总累计数据
Out_P:DWORD;	// 输出指针，&VBxx
Flag_P:DWORD;	// 整点值指针，&VBxx
END_VAR
VAR
Out_P_辅:DWORD;
Flag_P_辅:DWORD;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK MBUS_SERVER:SBR10
TITLE=
VAR_INPUT
Connect:BOOL;
IP_Port:WORD;
MaxIQ:WORD;
MaxAI:WORD;
MaxHold:WORD;
HoldStart:DWORD;
END_VAR
VAR_OUTPUT
Done:BOOL;
Error:BYTE;
END_VAR
VAR
temp_byte:BYTE;
temp_word:WORD;
temp_dword:DWORD;
byte_count:WORD;
len_trecv:WORD;
len_msg:WORD;
byteCount_msg:WORD;
temp_in:DWORD;
temp_out:DWORD;
temp_maxAddress:WORD;
quantity_data:WORD;
byte_offset:DWORD;
modbus_exception_code:BYTE;
pMax_Hold:DWORD;
pHold_Start:DWORD;
pSend_Length:DWORD;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK MBM1:SBR11
TITLE=This code is property of Siemens Industry, Inc. 
VAR_OUTPUT
crc:WORD;
END_VAR
VAR
count:INT;
ptr:DWORD;
END_VAR
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK 单机设备控制:SBR12
TITLE=电磁阀、风机、水泵分自动和手动控制
BEGIN
Network 1 
// 软化水补水电磁阀控制
LD     SM0.0
LD     M2.3
LDW<   VW8, VW1050
O      M2.6
ALD
AW<    VW8, VW1052
LDN    M2.3
LD     M2.4
O      M2.6
ALD
AN     M2.5
OLD
ALD
=      M2.6
Network 2 
// 屋顶风机控制
LD     SM0.0
LPS
LDN    M3.3
A      M3.4
LD     M3.3
LD     M3.0
OW>    VW20, VW1054
ALD
OLD
ALD
AN     T38
AN     M3.1
EU
TOF    T37, 30
LPP
LDN    M3.3
A      M3.5
LD     M3.3
AN     M3.0
AW<    VW20, VW1056
OLD
ALD
AN     T37
A      M3.1
EU
TOF    T38, 30
Network 3 
// 热回收泵控制
LD     SM0.0
LPS
LDN    M4.3
A      M4.4
LD     M4.3
A      M4.0
OLD
ALD
AN     T40
AN     M4.1
AN     M4.2
EU
TOF    T39, 30
LPP
LDN    M4.3
A      M4.5
LD     M4.3
AN     M4.0
OLD
ALD
AN     T39
A      M4.1
EU
TOF    T40, 30
Network 4 
// 输出映射
LD     SM0.0
LPS
A      M2.6
=      Q1.1
LRD
A      T37
=      Q0.7
LRD
A      T38
=      Q1.0
LRD
A      T39
=      Q0.3
LPP
A      T40
=      Q0.4
END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK 输入映射:SBR13
TITLE=子程序注释
BEGIN
Network 1 
// 程序段注释
LD     SM0.0
LPS
A      I0.7
=      M3.1
LRD
AN     I0.1
=      M4.1
LRD
A      I0.2
=      M4.2
LPP
A      I1.2
=      M3.0
END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK 数据计算:SBR14
TITLE=子程序注释
BEGIN
Network 1 
// 程序段注释
LD     SM0.0
LPS
MOVR   VD2004, LD0
AENO
+R     VD2008, LD0
AENO
MOVR   LD0, VD2016
+R     VD2012, VD2016
LRD
MOVR   VD2000, VD2020
/R     VD2016, VD2020
LRD
MOVR   VD2004, VD2100
AENO
/R     VD2016, VD2100
AENO
MOVR   VD2008, VD2104
AENO
/R     VD2016, VD2104
AENO
MOVR   VD2012, VD2108
/R     VD2016, VD2108
LPP
MOVR   VD2000, VD2024
AENO
*R     VD2100, VD2024
AENO
MOVR   VD2000, VD2028
AENO
*R     VD2104, VD2028
AENO
MOVR   VD2000, VD2032
*R     VD2108, VD2032
END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK 报警:SBR15
TITLE=子程序注释
BEGIN
Network 1 
LD     SM0.0
A      M4.2
=      M0.1
Network 2 
// 模拟量报警
LD     SM0.0
LD     M10.1
O      M10.2
O      M10.3
O      M10.4
O      M10.5
O      M10.6
O      M10.7
O      M11.0
O      M11.1
O      M11.2
O      M11.3
O      M11.4
O      M11.5
O      M11.6
O      M11.7
O      M12.0
O      M12.1
O      M12.2
O      M12.3
O      M12.4
O      M12.5
O      M12.6
O      M12.7
O      M10.0
O      M3.0
ALD
LPS
EU
S      M0.2, 1
LPP
=      M0.3
Network 3 
// 远程复位
LD     SM0.0
LD     I0.0
O      M0.4
ALD
R      M0.2, 1
Network 4 
// 输出映射
LD     SM0.0
LPS
A      M0.0
=      Q0.0
LRD
A      M0.1
=      Q0.1
LPP
A      M0.2
=      Q0.2
END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK SET_RTC_I:SBR17
TITLE=NAME: 	  Clock_INT
// VERSION: 1.0
// DATE:	  07/2015
//  
//  ##########################################################
//  LICENSE:
//  This program is distributed as freeware.
//  
//  Siemens makes no warranty, expressed or implied, with regard to this software. 
//  All implied warranties, including the warranties of merchantability and fitness for a 
//  particular use, are hereby excluded.
//  
//  Under no circumstances shall Siemens, or the authors of this product,
//  be liable for any incidental or consequential damages, nor for any damages.
//  ##########################################################
//  
// This routine simplify the usage of the MicroWin shipped "SET_RTC" function.
// So the user doesn`t need to convert the data to BCD format before deliver to the MicroWin "SET_RTC" function. 
// 
// This routine will read the user values and convert them from integer to BCD format and 
// set the clock with these values.
// 
// Requirements:
// - Program memory space:
// - SET_RTC_I  126 Byte
// - Full Library     236 Byte
// 
// The program has 1 input and 0 output.
// 
// NOTICE:
//  Parameter 
//  Address      This must be an address of a V-memory location. This address is used as the first byte of a table which
//                     has a length of 8 Bytes. Please ensure that the 8 byte behind are not used with other program parts.
//                       
//  Example:
//                             _____________
//  	           I   SET_RTC_I   I
//                            I                         I
//  I------I SM0.0 I-----I EN                   I
//                            I                         I
//                            I                         I
//                &VB0 --I Address            I
//                            I_____________I
// 
VAR_INPUT
Address:DWORD;	// Table address where the set value are deposited 
END_VAR
VAR
INDX:WORD;	// loop counter
Adr_Mem_1:DWORD;	// temp memory  address
Adr_Mem_2:DWORD;	// temp memory  address
tmp_W:WORD;	// temp memory
END_VAR
BEGIN
Network 1 
// Copy library memory address and user given address into temp variables
// Netzwerkkommentar
LD     SM0.0
MOVD   LD0, LD6
AENO
MOVD   &VB4000, LD10
Network 2 
// initialize the For..Next loop, Repeat 8x the code between FOR and NEXT
LD     SM0.0
FOR    LW4, +0, +7
Network 3 
// Read indirect the clock value, convert them to integer and save to the correspondent output byte
LD     SM0.0
BTI    *LD6, LW14
AENO
IBCD   LW14
AENO
ITB    LW14, *LD10
Network 4 
// Increment the memory pointer to the next clock value and output byte
LD     SM0.0
INCD   LD6
AENO
INCD   LD10
Network 5 
// repeat
NEXT
Network 6 
// set the clock with the clock data
LD     SM0.0
TODW   VB4000
Network 7 
// dummy network, only to reserve the 8 byte memory space in "cross reference" list
LDN    SM0.0
BMB    VB4000, VB4000, 8
AENO
MOVB   VB4007, VB4007
Network 8 
Network 9 
Network 10 
Network 11 
Network 12 
// Netzwerktitel
// Netzwerkkommentar
END_SUBROUTINE_BLOCK
SUBROUTINE_BLOCK READ_RTC_I:SBR18
TITLE=NAME: 	  Clock_INT
// VERSION: 1.0
// DATE:	  07/2015
//  
//  ##########################################################
//  LICENSE:
//  This program is distributed as freeware.
//  
//  Siemens makes no warranty, expressed or implied, with regard to this software. 
//  All implied warranties, including the warranties of merchantability and fitness for a 
//  particular use, are hereby excluded.
//  
//  Under no circumstances shall Siemens, or the authors of this product,
//  be liable for any incidental or consequential damages, nor for any damages.
//  ##########################################################
//  
// This routine simplify the usage of the MicroWin shipped "READ_RTC" function.
// So the user doesn`t need to convert the output data of the MicroWin "READ_RTC" function 
// from BCD to Integer anymore.
// 
// This routine reads the clock and output the data into integer format.
// The user could use the data directly for processing.
// 
// Requirements:
// - Program memory space:
//   - READ_RTC_I  126 Byte
//   - Full Library        236 Byte
//  
// The program has 1 input and 0 output.
// 
//  NOTICE:
//  Parameter 
//  Address      This must be an address of a V-memory location. This address is used as the first byte of a table which
//                     has a length of 8 Bytes. Please ensure that the 8 byte behind are not used with other program parts.
//                       
//  Example:
//                             _____________
//  	           I READ_RTC_I  I
//                            I                         I
//  I------I SM0.0 I-----I EN                   I
//                            I                         I
//                            I                         I
//                &VB0 --I Address            I
//                            I_____________I
// 
VAR_INPUT
Address:DWORD;	// Table address to fill with clock values 
END_VAR
VAR
INDX:WORD;	// loop counter
Adr_Mem_1:DWORD;	// temp memory  address
Adr_Mem_2:DWORD;	// temp memory  address
tmp_W:WORD;	// temp memory
END_VAR
BEGIN
Network 1 
// Copy library memory address and user given address into temp variables
// Netzwerkkommentar
LD     SM0.0
MOVD   &VB4000, LD6
AENO
MOVD   LD0, LD10
Network 2 
// read the current clock data
LD     SM0.0
TODR   VB4000
Network 3 
// initialize the For..Next loop, Repeat 8x the code between FOR and NEXT
LD     SM0.0
FOR    LW4, +0, +7
Network 4 
// Read indirect the clock value, convert them to integer and save to the correspondent output byte
LD     SM0.0
BTI    *LD6, LW14
AENO
BCDI   LW14
AENO
ITB    LW14, *LD10
Network 5 
// Increment the memory pointer to the next clock value and output byte
LD     SM0.0
INCD   LD6
AENO
INCD   LD10
Network 6 
// repeat
NEXT
Network 7 
// dummy network, only to reserve the 8 byte memory space in "cross reference" list
LDN    SM0.0
BMB    VB4000, VB4000, 8
AENO
MOVB   VB4007, VB4007
Network 8 
Network 9 
Network 10 
Network 11 
Network 12 
Network 13 
// Netzwerktitel
// Netzwerkkommentar
END_SUBROUTINE_BLOCK
INTERRUPT_BLOCK INT_0:INT0
TITLE=中断例程注释
BEGIN
Network 1 
// 程序段注释
END_INTERRUPT_BLOCK
INTERRUPT_BLOCK MBUSM2:INT1
TITLE=This code is property of Siemens Industry, Inc. 
BEGIN
Network 1 // 此 POU 针对编辑和查看设置了密码保护.

END_INTERRUPT_BLOCK
