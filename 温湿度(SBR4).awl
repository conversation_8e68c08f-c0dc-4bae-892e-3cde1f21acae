SUBROUTINE_BLOCK 温湿度:SBR4
TITLE=子程序注释
BEGIN
Network 1 
// 第一扫秒周期, 复位各标志位和起始位。通讯失败重试次数改为0（VB5257）
LD     SM0.0
LPS
A      SM0.1
R      V5286.0, 8
R      V5287.0, 8
LPP
MOVB   0, VB5257
Network 2 
// Modbus 主站初始化完成后, 启动读写指令
LD     V5286.0
EU
S      V5286.1, 1
Network 3 
// 网络标题
// 
// 网络注释     初始化Modbus 主站通信,
// 
// EN          使能：必须保证每一扫描周期都被使能（使用SM0.0)
// 
// Mode      模式：常为1，使能 Modbus 协议功能；为0 时恢复为系统 PPI 协议
// 
// Baud       波特率：设为9600，要与从站波特率对应
// 
// Parity       校验：校验方式选择 0＝无校验；1=奇检验；2=偶检验
// 
// Port          通讯口：0 为 Port 0，1 为 Port 1
// 
// Timeout    超时：主站等待从站响应的时间，以毫秒为单位，典型的设置值为1000毫秒（1秒），
//                             允许设置的范围为 1－32767。
//                 注意： 这个值必须设置足够大以保证从站有时间响应。
// 
// Done      完成位：初始化完成，此位会自动置1。可以用该位启动 MBUS_MSG 读写操作
// 
// Error       初始化错误代码（只有在 Done 位为1时有效）：0＝ 无错误
//                                                                                                                1＝ 校验选择非法
//                                                                                                                2＝ 波特率选择非法
//                                                                                                                3 =   超时无效
//                                                                                                                4＝ 模式选择非法
//                                                                                                                9 = 端口号无效
//                                                                                                               10 = 信号板端口1 缺失或未组态
LD     SM0.0
=      L60.0
LD     SM0.0
=      L63.7
LD     L60.0
CALL   SBR5, L63.7, 9600, 0, 0, 1000, V5286.0, VB5290
Network 4 
// 读取从站保持寄存器的数据
// 
// EN             使能：同一时刻只能有一个读写功能（即 MBUS_MSG）使能
//                   注意：建议每一个读写功能（即 MBUS_MSG）都用上一个 MBUS_MSG 指令的 Done 完成位来激
//                               活，以保证所有读写指令循环进行（见程序）。 
// 
// First           读写请求位：每一个新的读写请求必须使用脉冲触发 
// 
// Slave         从站地址：可选择的范围 1－247
// 
// RW           读写操作：0＝读， 1＝写
//                  注意：1. 开关量输出和保持寄存器支持读和写功能
//                              2. 开关量输入和模拟量输入只支持读功能
// 
// Addr          读写从站的数据地址：选择读写的数据类型 00001至0xxxx－开关量输出
//                                                                                               10001至1xxxx－开关量输入
//                                                                                               30001至3xxxx－模拟量输入
//                                                                                               40001至4xxxx－保持寄存器
// 
// Count       通讯的数据个数（位或字的个数）
//                  注意： Modbus主站可读/写的最大数据量为120个字（是指每一个 MBUS_MSG 指令）
// 
// DataPtr     数据指针：1. 如果是读指令，读回的数据放到这个数据区中
//                                   2. 如果是写指令，要写出的数据放到这个数据区中
// 
// Done        读写功能完成位
// 
// Error         错误代码 只有在 Done 位为1时，错误代码才有效
// 错误代码： 0＝无错误
//                      1＝响应校验错误
//                      2＝未用
//                      3＝接收超时（从站无响应）
//                      4＝请求参数错误（slave address,Modbus address,count,RW）
//                      5＝Modbus/自由口未使能 
//                      6＝Modbus正在忙于其它请求
//                      7＝响应错误（响应不是请求的操作）
//                      8＝响应CRC校验和错误
//                     11 = 端口号无效
//                     12 = 信号板端口 1 缺失或未组态     
// 
//                  101＝ 从站不支持请求的功能
//                  102＝ 从站不支持数据地址
//                  103＝ 从站不支持此种数据类型
//                  104＝ 从站设备故障
//                  105＝ 从站接受了信息，但是响应被延迟
//                  106＝ 从站忙，拒绝了该信息
//                  107＝ 从站拒绝了信息
//                  108＝ 从站存储器奇偶错误
// 
// 
// 
// 常见的错误及其错误代码：
// 
// 1. 如果多个 MBUS_MSG 指令同时使能会造成6号错误
// 
// 2. 从站 delay 参数设的时间过长会造成3号错误
// 
// 3. 从站掉电或不运行，网络故障都会造成3号错误
// 
// 读变频器状态
LD     V5286.1
O      V5286.5
=      L60.0
LD     V5286.1
EU
LD     V5286.5
EU
OLD
=      L63.7
LD     L60.0
CALL   SBR7, L63.7, 1, 0, 40001, 2, &VB60, V5286.2, VB5291
Network 5 
LD     V5286.2
R      V5286.1, 1
R      V5286.5, 1
Network 6 
LD     V5286.2
=      L60.0
LD     V5286.2
EU
=      L63.7
LD     L60.0
CALL   SBR7, L63.7, 4, 0, 40001, 14, &VB100, V5286.3, VB5291
Network 7 
LD     V5286.3
R      V5286.2, 1
Network 8 
LD     V5286.3
=      L60.0
LD     V5286.3
EU
=      L63.7
LD     L60.0
CALL   SBR7, L63.7, 2, 0, 40001, 14, &VB140, V5286.4, VB5291
Network 9 
LD     V5286.4
R      V5286.3, 1
Network 10 
LD     V5286.4
=      L60.0
LD     V5286.4
EU
=      L63.7
LD     L60.0
CALL   SBR7, L63.7, 3, 0, 40001, 14, &VB180, V5286.5, VB5291
Network 11 
LD     V5286.5
R      V5286.4, 1
Network 12 
// 数据规整
LD     SM0.0
MOVW   VW60, VW20
-I     +400, VW20
MOVW   VW62, VW22
Network 13 
// 环境温度_湿度报警
LD     SM0.0
LPS
AW<    VW20, VW1040
=      M12.4
LRD
AW>    VW20, VW1042
=      M12.5
LRD
AW<    VW22, VW1044
=      M12.6
LPP
AW>    VW22, VW1046
=      M12.7
Network 14 
// 4#楼流量地址规整
LD     SM0.0
CALL   SBR6, VW116, VW118, LD0
MOVR   LD0, VD130
*R     100.0, VD130
CALL   SBR6, VW120, VW122, VD134
MOVR   VD130, LD0
+R     VD134, LD0
*R     100.0, LD0
ROUND  LD0, VD300
CALL   SBR6, VW108, VW110, VD304
CALL   SBR6, VW100, VW102, VD308
CALL   SBR6, VW104, VW106, VD312
Network 15 
// 制水间流量地址规整
LD     SM0.0
CALL   SBR6, VW156, VW158, LD0
MOVR   LD0, VD170
*R     100.0, VD170
CALL   SBR6, VW160, VW162, VD174
MOVR   VD170, LD4
+R     VD174, LD4
*R     100.0, LD4
ROUND  LD4, VD340
CALL   SBR6, VW148, VW150, VD344
CALL   SBR6, VW140, VW142, VD348
CALL   SBR6, VW144, VW146, VD352
Network 16 
// 去2#楼流量地址规整
LD     SM0.0
CALL   SBR6, VW196, VW198, LD0
MOVR   LD0, VD210
*R     100.0, VD210
CALL   SBR6, VW200, VW202, VD214
MOVR   VD210, LD8
+R     VD214, LD8
*R     100.0, LD8
ROUND  LD8, VD380
CALL   SBR6, VW188, VW190, VD384
CALL   SBR6, VW180, VW182, VD388
CALL   SBR6, VW184, VW186, VD392
Network 17 
// 日周月季年统计
LD     SM0.0
CALL   SBR9, VD300, &VB316, &VB1500
CALL   SBR9, VD340, &VB356, &VB1520
CALL   SBR9, VD380, &VB396, &VB1540
END_SUBROUTINE_BLOCK
