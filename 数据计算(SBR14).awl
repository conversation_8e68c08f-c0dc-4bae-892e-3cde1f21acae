SUBROUTINE_BLOCK 数据计算:SBR14
TITLE=子程序注释
BEGIN
Network 1 
// 程序段注释
LD     SM0.0
LPS
MOVR   VD2004, LD0
AENO
+R     VD2008, LD0
AENO
MOVR   LD0, VD2016
+R     VD2012, VD2016
LRD
MOVR   VD2000, VD2020
/R     VD2016, VD2020
LRD
MOVR   VD2004, VD2100
AENO
/R     VD2016, VD2100
AENO
MOVR   VD2008, VD2104
AENO
/R     VD2016, VD2104
AENO
MOVR   VD2012, VD2108
/R     VD2016, VD2108
LPP
MOVR   VD2000, VD2024
AENO
*R     VD2100, VD2024
AENO
MOVR   VD2000, VD2028
AENO
*R     VD2104, VD2028
AENO
MOVR   VD2000, VD2032
*R     VD2108, VD2032
END_SUBROUTINE_BLOCK
