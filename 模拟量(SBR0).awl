SUBROUTINE_BLOCK 模拟量:SBR0
TITLE=模拟量转换
BEGIN
Network 1 
// 冷却水
LD     SM0.0
LPS
CALL   SBR1, AIW16, 27648, 5530, 100, 0, VW0
A      SM0.0
LPS
AW<    VW0, VW1000
=      M10.0
LPP
AW>    VW0, VW1002
=      M10.1
LRD
CALL   SBR1, AIW18, 27648, 5530, 100, 0, VW2
LRD
A      SM0.0
LPS
AW<    VW2, VW1004
=      M10.2
LPP
AW>    VW2, VW1006
=      M10.3
LRD
LPS
CALL   SBR1, AIW20, 27648, 5530, 1400, 0, LW0
AW>    LW0, 0
MOVW   LW0, VW4
+I     VW1090, VW4
LPP
AW=    LW0, 0
MOVW   VW1090, VW4
LRD
A      SM0.0
LPS
AW<    VW4, VW1008
=      M10.4
LPP
AW>    VW4, VW1010
=      M10.5
LRD
CALL   SBR1, AIW22, 27648, 5530, 2000, 0, VW6
LPP
A      SM0.0
LPS
AW<    VW6, VW1012
=      M10.6
LPP
AW>    VW6, VW1014
=      M10.7
Network 2 
// 锅炉补水
LD     SM0.0
LPS
CALL   SBR1, AIW24, 27648, 5530, 2000, 0, LW0
AW>    LW0, VW1096
MOVW   LW0, VW8
+I     VW1096, VW8
LRD
AW<=   LW0, VW1096
MOVW   LW0, VW8
+I     VW1096, VW8
LRD
A      SM0.0
LPS
AW<    VW8, VW1016
=      M11.0
LPP
AW>    VW8, VW1018
=      M11.1
LRD
CALL   SBR1, AIW26, 27648, 5530, 2000, 0, VW10
LPP
A      SM0.0
LPS
AW<    VW10, VW1020
=      M11.2
LPP
AW>    VW10, VW1022
=      M11.3
Network 3 
// 锅炉
LD     SM0.0
LPS
CALL   SBR1, AIW28, 27648, 5530, 160, 0, LW0
AW>    LW0, 0
MOVW   LW0, VW12
+I     VW1092, VW12
LRD
AW=    LW0, 0
MOVW   VW1092, VW12
LRD
A      SM0.0
LPS
AW<    VW12, VW1024
=      M11.4
LPP
AW>    VW12, VW1026
=      M11.5
LRD
LPS
CALL   SBR1, AIW30, 27648, 5530, 160, 0, LW0
AW>    LW0, 0
MOVW   LW0, VW14
+I     VW1094, VW14
LPP
AW=    LW0, 0
MOVW   VW1094, VW14
LRD
A      SM0.0
LPS
AW<    VW14, VW1028
=      M11.6
LPP
AW>    VW14, VW1030
=      M11.7
LRD
CALL   SBR1, AIW32, 27648, 5530, 100, 0, VW16
LRD
A      SM0.0
LPS
AW<    VW16, VW1032
=      M12.0
LPP
AW>    VW16, VW1034
=      M12.1
LRD
CALL   SBR1, AIW34, 27648, 5530, 100, 0, VW18
LPP
A      SM0.0
LPS
AW<    VW18, VW1036
=      M12.2
LPP
AW>    VW18, VW1038
=      M12.3
END_SUBROUTINE_BLOCK
